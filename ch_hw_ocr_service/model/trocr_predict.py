import os
import torch
from tqdm import tqdm
from PIL import Image
from glob import glob
from transformers import TrOCRProcessor, VisionEncoderDecoderModel
from base_common import LoggerFactory
from ch_hw_ocr_service.model.dataset import decode_text
from ch_hw_ocr_service.model.blur_match_utils import judge_strs, judge_pred, str_unification, init

log = LoggerFactory.get_logger('hw_ocr_service')
class Trocr_Predictor:
    def __init__(self, model_path):
        init(f"{model_path}vocab_sym.txt")
        self.processor = TrOCRProcessor.from_pretrained(model_path)
        self.vocab = self.processor.tokenizer.get_vocab()
        self.vocab_inp = {self.vocab[key]: key for key in self.vocab}
        self.model = VisionEncoderDecoderModel.from_pretrained(model_path,early_stopping=False)
        self.model.config.max_length = 128
        self.model.config.no_repeat_ngram_size = 0 #3 取消预测词组重复的限制
        self.model.config.length_penalty = 2.0 #2.0 不对预测长度进行惩罚
        
        self.model.eval()
        self.model.cuda()
        self.crossentropyloss = torch.nn.CrossEntropyLoss(reduction='none')
        self.max_label_length = 128
        
        vocab_choose = self.extract_vocab_range(f'{model_path}vocab_choose.txt')
        vocab_judge = self.extract_vocab_range(f'{model_path}vocab_judge.txt')
        vocab_letter = self.extract_vocab_range(f'{model_path}vocab_letter.txt')
        vocab_number = self.extract_vocab_range(f'{model_path}vocab_number.txt')
        self.vocab_range_dict = {'choose':vocab_choose,'judge':vocab_judge,'letter':vocab_letter,'number':vocab_number}
        
        
    def extract_vocab_range(self,txt=''):
        with open(txt,'r') as f:
            lines = f.readlines()
        vocab_range = {}
        for line in lines:
            vocab = line.strip()
            vocab_range[vocab] = self.vocab[vocab]
        return vocab_range
        
    def predict(self,imgs,labels=None):
        if labels is None:
            results = self.predict_no_label(imgs)
        else:
            results = self.predict_with_label(imgs,labels)
            
        #log.info(f'reg resuslts: {results}')
        return results
        
    def empty_cache(self):
        torch.cuda.empty_cache()
    def predict_no_label(self,imgs):
        pixel_values = self.processor(imgs, return_tensors="pt").pixel_values
        with torch.no_grad():
            generated_ids = self.model.generate(pixel_values[:, :, :].cuda(),num_return_sequences=1,num_beams=1,return_dict_in_generate=True,output_scores=True,early_stopping=False,length_penalty=1.0)
            #num_return_sequences输出几个预测结果，num_beams表示有几个备选预测，影响速度
        
        generated_texts = []
        for i in range(len(imgs)):
            generated_text = decode_text(generated_ids.sequences[i].cpu().numpy(), self.vocab, self.vocab_inp)
            
            final_str = ''
            for gt in generated_text:
                if gt == '<frac_start>':
                    final_str += '\\frac{'
                elif gt == '<frac_line>':
                    final_str += '}{'
                elif gt == '<frac_end>':
                    final_str += '}'
                elif gt == '<sqrt_start>':
                    final_str += '\\sqrt{'
                elif gt == '<sqrt_end>':
                    final_str += '}'
                elif gt == '²':
                    final_str += '^{2}'
                elif gt == '³':
                    final_str += '^{3}'
                else:
                    final_str += gt
            final_str = str_unification(final_str)
            generated_texts.append(final_str)
        # log.info(f'predict_no_label-generated_texts:{generated_texts}')
        return generated_texts
        
    def get_label_loss(self,img,label_str):
        label_str = label_str.replace('^2','²').replace('^{2}','²').replace('^{3}','³').replace('^3','³')
        
        pixel_values = self.processor([img], return_tensors="pt").pixel_values
        pixel_values = pixel_values.to('cuda:0')
        
        n = 0
        label = []
        frac_flag = False
        frac_top = False
        sqrt_flag = False
        while n < len(label_str):
            if not sqrt_flag and label_str[n:].startswith('\\sqrt{'):
                label.append('<sqrt_start>')
                sqrt_flag = True
                n += 6
                continue
            if sqrt_flag and label_str[n:].startswith('}'):
                label.append('<sqrt_end>')
                sqrt_flag = False
                n += 1
                continue
            if not frac_flag and label_str[n:].startswith('\\frac{'):
                label.append('<frac_start>')
                frac_flag = True
                frac_top = True
                n += 6
                continue
                
            if frac_flag and frac_top and label_str[n:].startswith('}{'):
                label.append('<frac_line>')
                frac_flag = True
                frac_top = False
                n += 2
                continue
                
            if frac_flag and not frac_top and label_str[n:].startswith('}'):
                label.append('<frac_end>')
                frac_flag = False
                frac_top = False
                n += 1
                continue
            label.append(label_str[n])
            n += 1
        
        ##### model methode

        lbl = [-100]*self.max_label_length
        lbl[0] = 0
        for i,l in enumerate(label[:self.max_label_length-3]):
            try:
                lbl[i+1] = self.vocab[l]
            except:
                continue
        lbl[i+2] = 2
        tensor_labels = torch.tensor(lbl).unsqueeze(0).to('cuda:0')
        
        with torch.no_grad():
            outputs = self.model(pixel_values=pixel_values,labels=tensor_labels)
        return outputs.loss.cpu().item()

    def predict_loss(self,imgs):
    
        pixel_values = self.processor(imgs, return_tensors="pt").pixel_values
        with torch.no_grad():
            generated_ids = self.model.generate(pixel_values[:, :, :].cuda(),num_return_sequences=1,num_beams=1,return_dict_in_generate=True,output_scores=True,early_stopping=False,length_penalty=1.0)
            #num_return_sequences输出几个预测结果，num_beams表示有几个备选预测，影响速度
        
        generated_texts = []
        losses_mean = []
        for i in range(len(imgs)):
            generated_text = decode_text(generated_ids.sequences[i].cpu().numpy(), self.vocab, self.vocab_inp)
            generated_texts.append(generated_text)
            if len(generated_text) > 30:#当识别的字符数量过长时，给一个极小loss避免绕过模糊匹配
                losses_mean.append(1e-9)
                continue
            logits = []
            for ii in range(len(generated_text)+2):
                logits.append(generated_ids.scores[ii][i])
            logits = torch.stack(logits,0).to('cuda:0')
            
            labels = [0]*(len(generated_text)+2)
            for it,tt in enumerate(generated_text):
                labels[it+1] = self.vocab[tt]
            labels[-1] = 2
            tensor_labels = torch.tensor(labels).to('cuda:0')
            
            loss = self.crossentropyloss(logits,tensor_labels)
            losses_mean.append(loss.mean().cpu().item())
        
        return generated_texts,losses_mean

    def predict_range(self,img,target='choose'):
        vocab_range = self.vocab_range_dict[target]
        vocabs = list(vocab_range.keys())
        vocabs_ind = [vocab_range[v] for v in vocabs]
        vocabs_ind = torch.tensor(vocabs_ind).long().cuda()
        pixel_values = self.processor([img], return_tensors="pt").pixel_values.cuda()
        with torch.no_grad():
            encoder_outputs = self.model.encoder(pixel_values=pixel_values,return_dict=True)
            decoder_input_ids = torch.tensor([[self.processor.tokenizer.bos_token_id]]).cuda() # 起始符<|endoftext|>
            # 自回归生成循环
            max_length = 20
            for _ in range(max_length):
                # 获取解码器输出
                decoder_outputs = self.model.decoder(
                    input_ids=decoder_input_ids,
                    encoder_hidden_states=encoder_outputs.last_hidden_state,
                    return_dict=True
                )
                
                # 预测下一个token
                next_token_logits = decoder_outputs.logits[:, -1, :]
                next_token_id = torch.argmax(next_token_logits, dim=-1, keepdim=True)
                # 遇到终止符则停止
                if next_token_id == self.processor.tokenizer.eos_token_id:
                    break
                if next_token_id == self.processor.tokenizer.bos_token_id:
                    decoder_input_ids = torch.cat([decoder_input_ids, next_token_id], dim=-1)
                    continue
                
                next_token_logits_range = next_token_logits[:,vocabs_ind]
                next_token_range = torch.argmax(next_token_logits_range, dim=-1, keepdim=True)
                vocab_pred = vocabs[next_token_range[0][0]] # A
                next_token_id = torch.tensor([[self.vocab[vocab_pred]]]).cuda() #tensor([[3812]]).cuda(0)
                loss = self.crossentropyloss(next_token_logits,next_token_id[0])
                # print('loss: ',loss,'--- vocab_pred: ',vocab_pred)
                if loss > 6:
                    break
                # 拼接生成结果
                decoder_input_ids = torch.cat([decoder_input_ids, next_token_id], dim=-1)
            # 解码输出结果
            caption = self.processor.tokenizer.decode(decoder_input_ids.squeeze(), skip_special_tokens=True)
            # print(f"生成的描述: {caption}")
            
        return caption

    def judge_range(self,img,lbl_,pred_txt):
        lbl = lbl_.replace('@@@','')
        target_range = ''
        for vocab_range in self.vocab_range_dict.keys():
            vocab_range_list = self.vocab_range_dict[vocab_range]
            
            target_flag = True
            for l in lbl:
                if not l in vocab_range_list:
                    target_flag = False
                    break
            
            if target_flag:
                target_range = vocab_range
                break
        if len(target_range) == 0:
            return pred_txt
            
        vocab_range_list = self.vocab_range_dict[target_range] # 如果原始预测的结果也在range分布范围，则不需要再识别一次
        pred_flag = True
        for p in pred_txt:
            if not p in vocab_range_list:
                pred_flag = False
                break
        if pred_flag:
            return pred_txt
        
        new_pred = self.predict_range(img,target_range)
        
        if len(new_pred) > 0:
            return new_pred
        else:
            return pred_txt        

    def predict_with_label(self,imgs,labels):
        pred_labels = []
        generated_texts,losses_mean = self.predict_loss(imgs)
        for i in range(len(imgs)):
            pred_text_list = generated_texts[i] #['1','2']
            pred_text = ''
            for gt in pred_text_list:
                if gt == '<frac_start>':
                    pred_text += '\\frac{'
                elif gt == '<frac_line>':
                    pred_text += '}{'
                elif gt == '<frac_end>':
                    pred_text += '}'
                elif gt == '<sqrt_start>':
                    pred_text += '\\sqrt{'
                elif gt == '<sqrt_end>':
                    pred_text += '}'
                elif gt == '²':
                    pred_text += '^{2}'
                elif gt == '³':
                    pred_text += '^{3}'
                else:
                    pred_text += gt
                    
            labels_i = labels[i].split('\t')
            
            pred_text = str_unification(pred_text)
                
            get_right_answer = False
            for lbl_ in labels_i:
                if lbl_.find('\\sqrt') != -1:
                    continue
                    
                lbl = str_unification(lbl_)
                lbl_ = lbl_.replace('@@@','')#多选题临时用@@@连接多个选项答案到一起，这里要返回结果了需要替换回去
                
                # print('$$$$$$$$$$$$$$$: ',lbl,pred_text)
                if judge_strs(lbl,pred_text):
                    pred_labels.append(lbl_)
                    get_right_answer = True
                    break

                pred_loss_mean = losses_mean[i]#第一预测答案的loss
                try:
                    label_loss_mean = self.get_label_loss(imgs[i], lbl_)  # 期望答案的loss,lbl为str
                    # judge=True认为可以模糊替换,else...
                    judge = judge_pred(label_loss_mean, pred_loss_mean, lbl_, pred_text)
                except:
                    judge = False
                # log.info('{} {} {} {} {}'.format(judge,lbl,label_loss_mean,pred_text,pred_loss_mean))
                if judge:
                    pred_labels.append(lbl_)
                    get_right_answer = True
                    break
                else:
                    #log.info('{} {} {} {} {}'.format(judge,lbl,label_loss_mean,pred_text,pred_loss_mean))
                    
                    pred_range = self.judge_range(imgs[i],lbl,pred_text)
                    # print(f'pred_range: {pred_range}')
                    if judge_strs(lbl,pred_range):
                        pred_labels.append(lbl_)
                        get_right_answer = True
                        break
                    else:
                        pred_text = pred_range
                    
            if not get_right_answer:
                pred_labels.append(pred_text)
        # log.info(f'predict_with_label-pred_labels:{pred_labels};labels:{labels}')
        return pred_labels
                
if __name__ == '__main__':
    predictor = Trocr_Predictor()
    
    '''
    imgs = [Image.open('0.jpg')]
    labels = ['房门']
    print(predictor.predict(imgs))
    print(predictor.predict(imgs,labels))
    '''


    paths = glob('../data/test_data/std_752/*.jpg')
    n = 0 
    r = 0
    #for i,path in tqdm(enumerate(paths)):
    for i,path in tqdm(enumerate(paths[:10000])):
        
        txt_file = os.path.splitext(path)[0]+'_space.txt'
        if not os.path.exists(txt_file):
            continue
        
        with open(txt_file) as f:
            text = f.read().strip().replace('xa0','')
        labels = text.split('\t')
        label_text = ''
        for gt in labels:
            if gt == '<frac_start>':
                label_text += '\\frac{'
            elif gt == '<frac_line>':
                label_text += '}{'
            elif gt == '<frac_end>':
                label_text += '}'
            else:
                label_text += gt
                
        if label_text.find('¥¥') != -1:
            continue
            
        path = '188.jpg'
        label_text = '642.8'
        #pred_str = predictor.predict([Image.open(path)],[label_text])[0]  
        try:
            pred_str = predictor.predict([Image.open(path)],[label_text])[0]

            label_text = label_text.replace('<frac_start>','\\F{').replace('<frac_line>','}{').replace('<frac_end>','}')
            pred_str = pred_str.replace('<frac_start>','\\F{').replace('<frac_line>','}{').replace('<frac_end>','}')
            print(label_text,pred_str)
            n += 1
            if label_text == pred_str:
                r += 1
            else:
                pass
                #print(path)
                #print(label_text,pred_str)
        except:
            continue
        break
    #print(r/n,r,n)

