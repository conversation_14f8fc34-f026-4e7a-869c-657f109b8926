# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_vertical_calculation.py
import time
import numpy as np
from PIL import Image

from base_common import LoggerFactory, ImageUtil, Constants, MissionMode
from base_common.service.service_of_base import BaseModelService
from base_common.service.service_of_redis import RedisManager
from print_ocr_rec_service.model.trocr_predict import Trocr_Predictor

log = LoggerFactory.get_logger('PrintOcrRecService')

class PrintOcrRecService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.PRINT_OCR_REC_MISSION
        model_path = f"{Constants.MODEL_WEIGHT_PATH}/print_ocr_rec_service/"
        self.runner = Trocr_Predictor(model_path)

    def replaceChar(self, strs):
        return strs.replace("？", "?").replace("％", "%"). \
            replace("十", "+").replace("，", ",").replace("（", "("). \
            replace("）", ")").replace("－", "-").replace("。", "."). \
            replace("÷", "/").replace("’", "'").replace("；", ";").replace("<", "＜")
    def do_post(self, data_json, is_ai_lab=False):
        t0 = time.time()
        try:
            rec_box = data_json.get('rec_box', [])
            image_np = self.get_image(data_json, is_ai_lab)
            imgs = []
            for box in rec_box:
                tmp_box = np.array(box).astype(np.float32)
                part_mat = ImageUtil.crop_image(image_np, tmp_box)
                img = Image.fromarray(part_mat)
                imgs.append(img)

            result_all = []
            predicted_labels = self.runner.predict(imgs)
            for j in range(len(predicted_labels)):
                pred_j = self.replaceChar(predicted_labels[j])
                result_all.append(pred_j)
            time_elapsed = time.time() - t0
            #log.info(f"box长度: {len(rec_box)}, 耗时:{time_elapsed:.4f} sec")
            return {"result": result_all, "time": time.time() - t0}
        except:
            return {"result": [], "time": time.time() - t0}
