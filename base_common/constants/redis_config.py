# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2025/3/24 11:46 
# @Description  : redis_config.py
import os
import json
import redis
import traceback
from ..mission_mode import MissionMode as M
from ..logger import LoggerFactory
log = LoggerFactory.get_logger('RedisConfig')

# 在2.9调试时，将IP改为公网IP即可
MISSION_DATA_HOST = "**************"
MISSION_QUEUE_HOST = "***************"
# MISSION_DATA_HOST = "************"
# MISSION_QUEUE_HOST = "************"
# 任务请求数据存储的redis 任务队列的redis
MISSION_DATA = 'mission_data'
MISSION_QUEUE = 'mission_queue'
AI_LAB_DATA = 'ai_lab_data'
AI_LAB_QUEUE = 'ai_lab_queue'
PASSWORD = 'Yunti2014'
class RedisConfig:
    def __init__(self, name=None, channel=None, host=None, port=None, password=None, database=None, max_connections=None):
        self._name = name
        self._channel = channel
        self._host = host
        self._port = port
        self._password = password
        self._database = database
        self._max_connections = max_connections
    def get_name(self): return self._name
    def get_channel(self): return self._channel
    def get_host(self): return self._host
    def get_port(self): return self._port
    def get_password(self): return self._password
    def get_database(self): return self._database
    def get_max_connections(self): return self._max_connections
    def __str__(self):
        return json.dumps({
            "name": self._name,
            "channel": self._channel,
            "host": self._host,
            "port": self._port,
            "password": self._password,
            "database": self._database,
            "max_connections": self._max_connections
        })

class RedisConfigs:
    __pools = {
        MISSION_DATA: {},
        MISSION_QUEUE: {},
        AI_LAB_DATA: {},
        AI_LAB_QUEUE: {}
    }

    __configs = {
        "product": [
            RedisConfig(MISSION_DATA, "default_channel", "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.VERTICAL_CALC_DET_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.VERTICAL_CALC_REC_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.QUICK_CALC_DET_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.QUICK_CALC_REC_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.PRINT_OCR_DET_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.PRINT_OCR_REC_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.DETECTION_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.DIRECTION_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.LINE_CALC_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.GRAPHICS_CALC_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.CH_HW_OCR_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.EN_HW_OCR_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.FORMULAR_HW_OCR_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.EMBEDDING_MISSION2, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.ANSWER_DETECT_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.SURF_MISSION, "*************", 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.ZONE_ALIGN_MISSION, "*************", 6379, PASSWORD, 8, 16),

            RedisConfig(MISSION_QUEUE, "default_channel", "*************", 6379, PASSWORD, 8, 128),

            RedisConfig(AI_LAB_DATA, "default_channel", "************", 6379, PASSWORD, 12, 16),
            RedisConfig(AI_LAB_QUEUE, "default_channel", "************", 6379, PASSWORD, 12, 128)
        ],
        "prepub": [
            RedisConfig(MISSION_DATA, "default_channel", MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.EMBEDDING_MISSION2, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.PRINT_OCR_DET_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.PRINT_OCR_REC_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.DETECTION_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.DIRECTION_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.VERTICAL_CALC_DET_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.VERTICAL_CALC_REC_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.QUICK_CALC_DET_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.QUICK_CALC_REC_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.LINE_CALC_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.GRAPHICS_CALC_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.CH_HW_OCR_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.EN_HW_OCR_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.FORMULAR_HW_OCR_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.ANSWER_DETECT_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.SURF_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),
            RedisConfig(MISSION_DATA, M.ZONE_ALIGN_MISSION, MISSION_DATA_HOST, 6379, PASSWORD, 8, 16),

            RedisConfig(MISSION_QUEUE, "default_channel", MISSION_QUEUE_HOST, 6379, PASSWORD, 8, 128),

            RedisConfig(AI_LAB_DATA, "default_channel", MISSION_DATA_HOST, 6379, PASSWORD, 12, 16),
            RedisConfig(AI_LAB_QUEUE, "default_channel", MISSION_QUEUE_HOST, 6379, PASSWORD, 12, 128)
        ]
    }

    @classmethod
    def print(cls):
        env = os.environ.get("EnvType", 'prepub')
        confs = cls.__configs.get(env, None)
        log.info(f"缓存配置: {json.dumps([json.loads(str(c)) for c in confs], indent=2, ensure_ascii=False)}")

    @classmethod
    def __get_pool(cls, redis_name, channel):
        try:
            env = os.environ.get("EnvType", 'prepub')
            confs = cls.__configs.get(env, None)
            if confs is None:
                raise Exception(f'redis env {env} is not in configs')
            __pools = cls.__pools[redis_name]
            if channel not in __pools:
                for conf in confs:
                    if conf.get_name() == redis_name and conf.get_channel() == channel:
                        __pool = redis.ConnectionPool(host=conf.get_host(), port=conf.get_port(),
                                                      password=conf.get_password(), db=conf.get_database(),
                                                      max_connections=conf.get_max_connections(), health_check_interval=30)
                        cls.__pools[redis_name][channel] = __pool
                        return __pool

            return __pools.get(channel)
        except:
            log.error(f'redis pool is None {traceback.format_exc()}')

    @classmethod
    def get_redis_client(cls, redis_name, channel='default_channel'):
        __pool = cls.__get_pool(redis_name, channel)
        if __pool is None:
            raise Exception(f'redis {redis_name} pool is None')
        return redis.StrictRedis(connection_pool=__pool)
