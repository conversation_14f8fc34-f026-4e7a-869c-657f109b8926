# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/8/23 19:48 
# @Description  : queue_consumer.py
import json
import time
import traceback
import threading

import tornado.web
import tornado.ioloop
import tornado.httpserver

from ..context import Context
from ..ok_http_util import OkHttpUtil, ThreadExecutor
from ..logger import LoggerFactory
from .mission_handler import MissionHandler
from ..mission_mode import MissionMode
from ..constants.constants import Constants

log = LoggerFactory.get_logger('MissionConsumer')
stop_flag = False

class StopController(tornado.web.RequestHandler):
    def get(self):
        global stop_flag
        stop_flag = True
        log.info(f'停止获取任务')
        self.write('task stoped.')

    def post(self):
        global stop_flag
        stop_flag = True
        log.info(f'停止获取任务')
        self.write('task stoped.')
def _make_app():
    handlers = [(r"/stop", StopController)]
    return tornado.web.Application(handlers)

class MissionConsumer:
    def __init__(self, mission_executor, mission_req_channel, port, no_torch=False, timeout=16):
        self.mission_queue_redis = None
        self.mission_req_channel = mission_req_channel
        self.mission_name = MissionMode.get_mission_name(self.mission_req_channel)
        self.mission_executor = mission_executor
        self.no_torch = no_torch
        self.timeout = timeout
        self.service_port = port
        self.mission_flag = {}
        self.redisRequest = None
        self.time_watcher_executor = ThreadExecutor(max_workers=4)
        self.mission_timeout = 5
        self.mission_timeout_counter = 0
        self.ai_lab_redis = None
        self.__cuda_error = f"当前服务器显卡异常，{self.mission_name}已停止接收任务，服务器IP：{Constants.LOCAL_IP}"
        self.__error_message = f"{self.mission_name}任务执行超过{self.timeout}秒，本次任务终止并归还任务池，当前服务已停止接收任务，服务器IP：{Constants.LOCAL_IP}"

    def init_redis(self, mission_queue_redis, ai_lab_redis=None):
        self.mission_queue_redis = mission_queue_redis
        self.ai_lab_redis = ai_lab_redis
        if ai_lab_redis is not None:
            self._subscriber_listen(ai_lab_redis)
        self.redisRequest = MissionHandler(mission_queue_redis, ai_lab_redis, self.mission_req_channel)

    def start_web(self):
        global stop_flag
        try:
            log.info(f"开始启动web服务，端口{self.service_port}")
            application = _make_app()
            try:
                server_app = tornado.httpserver.HTTPServer(application)
                server_app.bind(self.service_port)
                server_app.start()
                log.info(f"{self.mission_req_channel} {self.service_port}已启动.")
                tornado.ioloop.IOLoop.current().start()
            except KeyboardInterrupt:
                log.info(f"请求终止{self.mission_req_channel} {self.service_port}")
                stop_flag = True
        except:
            pass

    def start(self, stop_time='0'):
        global stop_flag
        web_thread = threading.Thread(target=self.start_web)
        web_thread.daemon = True
        web_thread.start()
        self._subscriber_listen(self.mission_queue_redis)
        self.redisRequest.set_stop_time(stop_time)
        # 开始接收任务
        if self.ai_lab_redis is not None:
            self.redisRequest.listen_two_queue(self.on_message, self.is_stop)
        else:
            self.redisRequest.listen(self.on_message, self.is_stop)

    def _subscriber_listen(self, redis_service):
        subscriber_thread = threading.Thread(target=self.__subscriber_listen, args=(redis_service,))
        subscriber_thread.daemon = True
        subscriber_thread.start()

    def __subscriber_listen(self, redis_service):
        global stop_flag
        pubsub = redis_service.pubsub()
        pubsub.subscribe(self.mission_req_channel)
        log.info(f"开始监听{self.mission_req_channel}频道")
        try:
            for _ in pubsub.listen():
                try:
                    if stop_flag:
                        pubsub.unsubscribe(self.mission_req_channel)
                        break
                except:
                    log.error(f"Error while listening to Redis pubsub: {traceback.format_exc()}")
        except:
            log.error(f"2Error while listening to Redis pubsub: {traceback.format_exc()}")
    def is_stop(self):
        global stop_flag
        return stop_flag

    def time_watcher(self, mission_req, timeout):
        global stop_flag
        time.sleep(timeout)
        mission_id = mission_req['mission_id']
        if mission_id not in self.mission_flag or self.mission_flag[mission_id] != 1 or Constants.LOCAL_IP in Constants.FIX_IP_LIST:
            # 只有在任务正在进行时标记为超时
            return
        log.error(self.__error_message)
        self.mission_flag[mission_id] = 2
        record_id = mission_req.get('record_id', None)
        if Context.is_product():
            if record_id is None:
                OkHttpUtil.notify_feishu(self.__error_message)
            else:
                OkHttpUtil.notify_feishu(f"{self.__error_message} record_id: {record_id}")
        stop_flag = True
        self.redisRequest.stop()
        self.mission_queue_redis.right_push(self.mission_req_channel, json.dumps(mission_req))

    def on_message(self, mission_req, is_ai_lab=False):
        t0 = time.time()
        mission_resp = {'mission_id': None, 'mission_data': None,'mission_cost': -1, 'mission_result': False}
        mission_resp_channel = mission_req.get('mission_channel', None)
        if Context.is_product() and not is_ai_lab:
            try:
                self.mission_timeout_counter += 1
                if self.mission_timeout_counter >= 50:
                    val = self.mission_queue_redis.get_val('CONST:MODEL_MISSION_TIMEOUT')
                    self.mission_timeout = int(val) if val is not None else 5
                    self.mission_timeout_counter = 0
                mission_create = mission_req.get('mission_create', None)
                if mission_create is not None and time.time() - mission_create > self.mission_timeout:
                    log.warn(f"{self.mission_name}任务创建时间过长，丢弃此任务")
                    if mission_resp_channel is not None:
                        self.mission_queue_redis.right_push(mission_resp_channel, json.dumps(mission_resp))
                    return
            except:
                pass
        mission_cost = 0
        time_future = None
        try:
            mission_id = mission_req['mission_id']
            self.mission_flag[mission_id] = 1
            if self.timeout != -1 and not is_ai_lab:
                time_future = self.time_watcher_executor.submit(self.time_watcher, mission_req, self.timeout)
            log.debug(f'{self.mission_name} 端口{self.service_port} 收到新任务[{mission_id}]')
            req_data = mission_req['mission_data']
            req_data = json.loads(req_data) if isinstance(req_data, str) else req_data

            mission_data = self.mission_executor(req_data, is_ai_lab)
            mission_cost = time.time() - t0
            log.debug(f'{self.mission_name} 端口{self.service_port} 任务[{mission_id}]已完成，耗时：{mission_cost:.4f} sec')
            if Context.is_product() and mission_cost > 5:
                log.warn(f'{self.mission_name} {Constants.LOCAL_IP} 任务耗时过长 {mission_cost:.4f} sec。')

            mission_resp.update({
                'mission_id': mission_id,
                'mission_data': mission_data,
                'mission_cost': mission_cost,
                'mission_result': True
            })
            #log.info(f'{self.mission_name} 端口{self.service_port} 任务完成 {mission_cost:.4f} sec。')
            if self.mission_flag[mission_id] == 2:
                log.info(f'{self.mission_name} 端口{self.service_port} 任务超时，停止接收新的任务。')
        except:
            if mission_cost == 0:
                mission_cost = time.time() - t0
            mission_resp.update({'mission_id': mission_id, 'mission_cost': mission_cost})
            log.error(f'{self.mission_name} 端口{self.service_port} 任务异常 \r\n{traceback.format_exc()}')
        finally:
            self.__release(mission_req, mission_resp, mission_resp_channel, mission_id, time_future, is_ai_lab)
    def __release(self, mission_req, mission_resp, mission_resp_channel, mission_id, time_future, is_ai_lab=False):
        global stop_flag
        try:
            log.info(f'{"AI_LAB" if is_ai_lab else "拍照批改"} {self.mission_name} 端口{self.service_port} 响应队列{mission_resp_channel} 任务完成 {mission_resp["mission_cost"]:.4f} sec。')
            if mission_resp_channel is not None:
                mission_resp.update({'mission_ip': Constants.LOCAL_IP})
                if not is_ai_lab:
                    self.mission_queue_redis.right_push(mission_resp_channel, json.dumps(mission_resp, ensure_ascii=False))
                else:
                    self.ai_lab_redis.right_push(mission_resp_channel, json.dumps(mission_resp, ensure_ascii=False))
            del self.mission_flag[mission_id]
            if time_future is not None and not time_future.done():
                time_future.cancel()
            if not self.no_torch:
                import torch
                import torch.cuda
                torch.cuda.empty_cache()
        except Exception as e:
            error_info = str(e)
            if isinstance(e, RuntimeError) and "CUDA error" in error_info:
                stop_flag = True
                log.error(f"CUDA error encountered: {error_info}")
                if Context.is_product():
                    OkHttpUtil.notify_feishu(self.__cuda_error)
                if not is_ai_lab:
                    self.mission_queue_redis.right_push(self.mission_req_channel, json.dumps(mission_req))
                else:
                    self.ai_lab_redis.right_push(self.mission_req_channel, json.dumps(mission_req))
            else:
                log.error(f"An error occurred: {error_info}")
