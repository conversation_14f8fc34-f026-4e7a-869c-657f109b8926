# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/11/22 9:20 
# @Description  : auto_setting.py
import json
import time
import threading
import traceback

from .service.service_of_redis import RedisManager
from .logger import LoggerFactory
log = LoggerFactory.get_logger("AutoSetting")

class AutoSetting:
    one_piece_print_ocr_size = 8
    one_piece_ch_ocr_size = 6
    one_piece_en_ocr_size = 4
    one_piece_formula_ocr_size = 8
    one_piece_quick_rec_size = 4
    exculde_threshold = 0.3
    total_score_threshold = 0.45
    cdn_internal_host_map = {
        'https://cdnbj.bookln.cn': "https://ajlivebj.oss-cn-beijing-internal.aliyuncs.com"
    }
    redis_service = None
    @staticmethod
    def get_config():
        return {
            "one_piece_print_ocr_size": AutoSetting.one_piece_print_ocr_size,
            "one_piece_ch_ocr_size": AutoSetting.one_piece_ch_ocr_size,
            "one_piece_en_ocr_size": AutoSetting.one_piece_en_ocr_size,
            "one_piece_formula_ocr_size": AutoSetting.one_piece_formula_ocr_size,
            "one_piece_quick_rec_size": AutoSetting.one_piece_quick_rec_size,
            "exculde_threshold": AutoSetting.exculde_threshold,
            "cdn_internal_host_map": AutoSetting.cdn_internal_host_map,
            "total_score_threshold": AutoSetting.total_score_threshold
        }
    @staticmethod
    def setup():
        AutoSetting.redis_service = RedisManager.get_mission_queue_redis()
        schedule_thread_listener = threading.Thread(target=AutoSetting.update_config)
        schedule_thread_listener.daemon = True
        schedule_thread_listener.start()

    @staticmethod
    def update_config():
        while AutoSetting.redis_service is not None:
            try:
                val = AutoSetting.redis_service.get_val("CONST:OCR_PRINT_BATCH_SIZE")
                AutoSetting.one_piece_print_ocr_size = int(val) if val is not None else 6

                val = AutoSetting.redis_service.get_val("CONST:OCR_CH_BATCH_SIZE")
                AutoSetting.one_piece_ch_ocr_size = int(val) if val is not None else 6

                val = AutoSetting.redis_service.get_val("CONST:OCR_EN_BATCH_SIZE")
                AutoSetting.one_piece_en_size = int(val) if val is not None else 4

                val = AutoSetting.redis_service.get_val("CONST:OCR_FORMULA_BATCH_SIZE")
                AutoSetting.one_piece_formula_size = int(val) if val is not None else 8

                val = AutoSetting.redis_service.get_val("CONST:QUICK_REC_BATCH_SIZE")
                AutoSetting.one_piece_quick_rec_size = int(val) if val is not None else 4

                val = AutoSetting.redis_service.get_val("CONST:EXCULDE_EMBEDDING_THRESHOLD")
                AutoSetting.exculde_threshold = float(val) if val is not None else 0.3

                val = AutoSetting.redis_service.get_val("CONST:SEARCH_TOTAL_THRESHOLD")
                AutoSetting.total_score_threshold = float(val) if val is not None else 0.45

                val = AutoSetting.redis_service.get_val("CONST:CDN_INTERNAL_HOST_MAP")
                AutoSetting.cdn_internal_host_map = json.loads(val) if val is not None else {
                    'https://cdnbj.bookln.cn': "https://ajlivebj.oss-cn-beijing-internal.aliyuncs.com"
                }
                time.sleep(300)
            except:
                log.error(f"从redis读取最新阈值失败，使用默认值{traceback.format_exc()}")
                AutoSetting.one_piece_print_ocr_size = 8
                AutoSetting.one_piece_ch_ocr_size = 6
                AutoSetting.one_piece_en_ocr_size = 4
                AutoSetting.one_piece_formula_ocr_size = 8
                AutoSetting.one_piece_quick_rec_size = 4
                AutoSetting.exculde_threshold = 0.3
                AutoSetting.total_score_threshold = 0.45
            log.debug(f"当前BatchSize "
                     f"印刷体识别:{AutoSetting.one_piece_print_ocr_size} "
                     f"中文识别:{AutoSetting.one_piece_ch_ocr_size} "
                     f"英文识别:{AutoSetting.one_piece_en_ocr_size} "
                     f"公式识别:{AutoSetting.one_piece_formula_ocr_size} "
                     f"口算识别:{AutoSetting.one_piece_quick_rec_size} "
                     f"Embedding排除阈值:{AutoSetting.exculde_threshold}"
                     f"搜页总得分阈值:{AutoSetting.total_score_threshold}"
                     f"CDN内网域名替换:{json.dumps(AutoSetting.cdn_internal_host_map)}")
