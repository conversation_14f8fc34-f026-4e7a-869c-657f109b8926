# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 11:00 

import json
import time

import numpy as np
import traceback

from base_common import LoggerFactory, Constants, Context, MissionMode
from answer_det_service.model.dbnet_interface import Dbnet_Interface
from base_common.service.service_of_base import BaseModelService

log = LoggerFactory.get_logger('AnswerDetectService')

def default_dump(obj):
    """Convert numpy classes to JSON serializable objects."""
    if isinstance(obj, (np.integer, np.floating, np.bool_)):
        return obj.item()
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj
class AnswerDetectService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.ANSWER_DETECT_MISSION
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/answer_det_service/model_best.pth'
        self.dbnet_txt = Dbnet_Interface(model_path=model_path, vis_handwrite_detect=Constants.SAVE_VIS)

    def do_post(self, data_json, is_ai_lab=False):
        try:
            t0 = time.time()
            img_photo_cv2 = self.get_image(data_json, is_ai_lab)
            t1 = time.time()
            redis_cost = t1 - t0
            log.info(f'get redis cost:{redis_cost:0.4f} sec')
            boxs_list_4p, boxs_list_2p, boxs_list_np = self.dbnet_txt.detect_img(img_photo_cv2)
        except:
            log.error(f'{traceback.format_exc()}')
        
        output = json.dumps({
            'boxs_list_4p': boxs_list_4p,
            'boxs_list_2p': boxs_list_2p,
            'boxs_list_np': boxs_list_np
        }, ensure_ascii=False, default=default_dump)
        Context.print_cost(self.mission_mode, redis_cost, time.time() - t0)
        return output
