# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2025/4/2 8:45 
# @Description  : service_of_surf.py
import json
import time

from base_common.mission_mode import MissionAlisa
from base_common.service.service_of_base import BaseService
from base_common import Context, LoggerFactory, MissionMode, RedisRequestor

log = LoggerFactory.get_logger('SurfService')

class SurfService(BaseService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.SURF_MISSION
        self.init_redis()

    def do_surf(self, book_data, req_data, pdf_path, w, h):
        t0 = time.time()
        base_path = book_data.get_base_path()
        page_data = book_data.get_page_data()
        img_key, img_type = self.set_image(page_data.get_img_page(), mission_id=req_data.get_mission_id(), img_fmt='.png')
        data_json = {
            'user_img_key': img_key,
            'img_type': img_type,
            'pdf_path': pdf_path,
            'page_id': page_data.get_page_id(),
            'crop_rect': page_data.get_crop_rect(),
            'base_path': base_path,
            'col_index': page_data.get_col_index(),
            'width': w,
            'height': h
        }
        model_response = RedisRequestor.ai_align_images_surf(data_json, req_data.get_record_id())

        aligned_page = None
        if model_response.is_success():
            json_data = model_response.get_json_response()
            log.debug(f"surf response: {json.dumps(json_data)}")
            img = self.get_image(json_data)
            aligned_page = (
                img,
                json_data.get('crop_box', []),
                json_data.get('surf_mat', None),
                json_data.get('img_key', None),
                json_data.get('img_type', None)
            )

        Context.report_cost(req_data.get_mission_id(), (MissionAlisa.SURF_SERVICE, time.time() - t0))
        return aligned_page