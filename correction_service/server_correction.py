# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> yanhui
# @Date         ：2024/6/4 10:54
import os
import shutil
import asyncio
import tornado.web
from tornado.platform.asyncio import AnyThreadEventLoopPolicy
from correction_service.common.logo import G<PERSON><PERSON>, RESET

from base_common.auto_setting import AutoSetting
from base_common import Constants, Context, ServerWrapper, RedisRequestor, OssUtil, LoggerFactory
from correction_service.pool import ServicePool
from correction_service.common import CorrectionContext
from correction_service.controller import CorrectionController

log = LoggerFactory.get_logger('CorrectionController')
def make_app():
    handlers = [(r'/correct', CorrectionController)]
    CorrectionController.initialize_executor(Constants.CORRECTION_MISSION_NUMBER * 2)
    return tornado.web.Application(handlers, max_body_size=100 * 1024 * 1024, max_buffer_size=100 * 1024 * 1024)

def setup():
    if os.path.exists(Constants.TEMP_PATH):
        shutil.rmtree(Constants.TEMP_PATH)
    Context.setup()
    AutoSetting.setup()
    CorrectionContext.setup()
    Constants.setup(Context.is_product())
    OssUtil.load_oss_config()
    ServicePool.initialize(Constants.CORRECTION_MISSION_NUMBER * 2)
    RedisRequestor.init_producers()

if __name__ == '__main__':
    print(f"{GREEN}系统已初始化.{RESET}")
    asyncio.set_event_loop_policy(AnyThreadEventLoopPolicy())
    server_warapper = ServerWrapper(log, '拍照批改主程序', 8889)
    log.info(f"拍照批改主程序监听http://0.0.0.0:8886/correct")
    server_warapper.start_server(make_app, setup)
